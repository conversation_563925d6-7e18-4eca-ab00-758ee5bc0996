// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ownedGroups    Group[]       @relation("GroupOwner")
  memberships    GroupMember[]
  messages       Message[]
  notes          Note[]
  noteBlocks     NoteBlock[]

  @@map("users")
}

model Group {
  id          String   @id @default(cuid())
  name        String
  description String?
  avatar      String?
  isPrivate   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  owner    User   @relation("GroupOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  ownerId  String
  members  GroupMember[]
  messages Message[]
  notes    Note[]

  @@map("groups")
}

model GroupMember {
  id     String @id @default(cuid())
  role   Role   @default(MEMBER)
  joinedAt DateTime @default(now())

  // Relations
  user    User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId  String
  group   Group  @relation(fields: [groupId], references: [id], onDelete: Cascade)
  groupId String

  @@unique([userId, groupId])
  @@map("group_members")
}

model Message {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Thread support fields
  parentMessageId String?  // ID of parent message (null for main chat messages)
  threadId        String?  // ID of thread (same as parentMessageId for all messages in a thread)
  isThreadStarter Boolean @default(false) // Marks the message that starts a thread

  // Relations
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String
  group    Group  @relation(fields: [groupId], references: [id], onDelete: Cascade)
  groupId  String

  // Thread relations
  parentMessage Message? @relation("MessageThread", fields: [parentMessageId], references: [id], onDelete: Cascade)
  replies       Message[] @relation("MessageThread")

  @@index([groupId, parentMessageId]) // For efficient thread queries
  @@index([threadId]) // For efficient thread message queries
  @@map("messages")
}

model Note {
  id          String   @id @default(cuid())
  title       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  author   User        @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String
  group    Group       @relation(fields: [groupId], references: [id], onDelete: Cascade)
  groupId  String
  blocks   NoteBlock[]

  @@map("notes")
}

model NoteBlock {
  id       String    @id @default(cuid())
  type     BlockType @default(TEXT)
  content  String
  order    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  note     Note   @relation(fields: [noteId], references: [id], onDelete: Cascade)
  noteId   String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String

  @@map("note_blocks")
}

enum Role {
  OWNER
  ADMIN
  MEMBER
}

enum BlockType {
  TEXT
  HEADING_1
  HEADING_2
  HEADING_3
  BULLET_LIST
  NUMBERED_LIST
  CODE
  QUOTE
}
