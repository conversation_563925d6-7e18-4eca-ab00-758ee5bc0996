'use client'

import React, { createContext, useContext, useState } from 'react'
import { Message<PERSON>ithAuthor, ThreadInfo, ThreadWithMessages } from '@/types'

interface ThreadContextType {
  // Thread list management
  threads: ThreadInfo[]
  threadsLoading: boolean
  loadThreads: (groupId: string, page?: number) => Promise<void>
  
  // Current thread management
  currentThread: ThreadWithMessages | null
  threadMessages: MessageWithAuthor[]
  threadLoading: boolean
  threadSending: boolean
  
  // Thread operations
  openThread: (messageId: string) => Promise<void>
  closeThread: () => void
  sendThreadMessage: (messageId: string, content: string) => Promise<void>
  loadThreadMessages: (messageId: string, page?: number) => Promise<void>
  
  // Thread state
  isThreadOpen: boolean
  currentThreadId: string | null
  
  // Pagination
  threadsHasMore: boolean
  threadsCurrentPage: number
  threadMessagesHasMore: boolean
  threadMessagesCurrentPage: number
}

const ThreadContext = createContext<ThreadContextType | undefined>(undefined)

export function ThreadProvider({ children }: { children: React.ReactNode }) {
  // Thread list state
  const [threads, setThreads] = useState<ThreadInfo[]>([])
  const [threadsLoading, setThreadsLoading] = useState(false)
  const [threadsHasMore, setThreadsHasMore] = useState(false)
  const [threadsCurrentPage, setThreadsCurrentPage] = useState(1)
  
  // Current thread state
  const [currentThread, setCurrentThread] = useState<ThreadWithMessages | null>(null)
  const [threadMessages, setThreadMessages] = useState<MessageWithAuthor[]>([])
  const [threadLoading, setThreadLoading] = useState(false)
  const [threadSending, setThreadSending] = useState(false)
  const [isThreadOpen, setIsThreadOpen] = useState(false)
  const [currentThreadId, setCurrentThreadId] = useState<string | null>(null)
  const [threadMessagesHasMore, setThreadMessagesHasMore] = useState(false)
  const [threadMessagesCurrentPage, setThreadMessagesCurrentPage] = useState(1)

  const loadThreads = async (groupId: string, page: number = 1) => {
    setThreadsLoading(true)
    try {
      const response = await fetch(`/api/groups/${groupId}/threads?page=${page}&limit=20`)
      if (response.ok) {
        const data = await response.json()
        
        if (page === 1) {
          setThreads(data.threads)
        } else {
          setThreads(prev => [...prev, ...data.threads])
        }
        
        setThreadsHasMore(data.pagination.hasMore)
        setThreadsCurrentPage(page)
      } else {
        console.error('Failed to load threads')
      }
    } catch (error) {
      console.error('Failed to load threads:', error)
    } finally {
      setThreadsLoading(false)
    }
  }

  const openThread = async (messageId: string) => {
    if (currentThreadId === messageId && isThreadOpen) {
      return // Thread already open
    }

    setThreadLoading(true)
    setCurrentThreadId(messageId)
    setIsThreadOpen(true)
    
    try {
      await loadThreadMessages(messageId, 1)
    } catch (error) {
      console.error('Failed to open thread:', error)
      closeThread()
    }
  }

  const closeThread = () => {
    setIsThreadOpen(false)
    setCurrentThreadId(null)
    setCurrentThread(null)
    setThreadMessages([])
    setThreadMessagesCurrentPage(1)
    setThreadMessagesHasMore(false)
  }

  const loadThreadMessages = async (messageId: string, page: number = 1) => {
    setThreadLoading(true)
    try {
      const response = await fetch(`/api/messages/${messageId}/thread?page=${page}&limit=50`)
      if (response.ok) {
        const data = await response.json()
        
        if (page === 1) {
          setThreadMessages(data.messages)
          setCurrentThread({
            parentMessage: data.parentMessage,
            messages: data.messages,
            messageCount: data.pagination.totalMessages,
            participants: data.participants
          })
        } else {
          setThreadMessages(prev => [...data.messages, ...prev])
        }
        
        setThreadMessagesHasMore(data.pagination.hasMore)
        setThreadMessagesCurrentPage(page)
      } else {
        console.error('Failed to load thread messages')
      }
    } catch (error) {
      console.error('Failed to load thread messages:', error)
    } finally {
      setThreadLoading(false)
    }
  }

  const sendThreadMessage = async (messageId: string, content: string) => {
    setThreadSending(true)
    try {
      const response = await fetch(`/api/messages/${messageId}/thread`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ content }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to send thread message')
      }

      const result = await response.json()
      
      // Add new message to the thread
      setThreadMessages(prev => [...prev, result.data])
      
      // Update current thread message count
      if (currentThread) {
        setCurrentThread(prev => prev ? {
          ...prev,
          messageCount: prev.messageCount + 1,
          messages: [...prev.messages, result.data]
        } : null)
      }
    } catch (error) {
      console.error('Failed to send thread message:', error)
      throw error
    } finally {
      setThreadSending(false)
    }
  }

  return (
    <ThreadContext.Provider value={{
      // Thread list
      threads,
      threadsLoading,
      loadThreads,
      threadsHasMore,
      threadsCurrentPage,
      
      // Current thread
      currentThread,
      threadMessages,
      threadLoading,
      threadSending,
      openThread,
      closeThread,
      sendThreadMessage,
      loadThreadMessages,
      isThreadOpen,
      currentThreadId,
      threadMessagesHasMore,
      threadMessagesCurrentPage,
    }}>
      {children}
    </ThreadContext.Provider>
  )
}

export function useThread() {
  const context = useContext(ThreadContext)
  if (context === undefined) {
    throw new Error('useThread must be used within a ThreadProvider')
  }
  return context
}
