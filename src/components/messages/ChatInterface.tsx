'use client'

import { useState, useEffect, useRef } from 'react'
import { useMessages } from '@/contexts/MessageContext'
import { useAuth } from '@/contexts/AuthContext'
import { useThread } from '@/contexts/ThreadContext'
import { GroupWithMembers } from '@/types'
import MessageActions, { ThreadIndicator } from './MessageActions'

interface ChatInterfaceProps {
  group: GroupWithMembers
  onThreadOpen?: (messageId: string) => void
}

export default function ChatInterface({ group, onThreadOpen }: ChatInterfaceProps) {
  const { messages, loading, sending, sendMessage, loadMessages, hasMore, currentPage } = useMessages()
  const { user } = useAuth()
  const { openThread } = useThread()
  const [newMessage, setNewMessage] = useState('')
  const [error, setError] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (group) {
      loadMessages(group.id)
    }
  }, [group.id])

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || sending) return

    setError('')
    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      await sendMessage(group.id, messageContent)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message')
      setNewMessage(messageContent) // Restore message on error
    }
  }

  const loadMoreMessages = async () => {
    if (hasMore && !loading) {
      await loadMessages(group.id, currentPage + 1)
    }
  }

  const formatTime = (date: string | Date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const formatDate = (date: string | Date) => {
    const messageDate = new Date(date)
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Today'
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday'
    } else {
      return messageDate.toLocaleDateString()
    }
  }

  const shouldShowDateSeparator = (currentMessage: any, previousMessage: any) => {
    if (!previousMessage) return true

    const currentDate = new Date(currentMessage.createdAt).toDateString()
    const previousDate = new Date(previousMessage.createdAt).toDateString()

    return currentDate !== previousDate
  }

  const handleReply = async (messageId: string) => {
    try {
      await openThread(messageId)
      onThreadOpen?.(messageId)
    } catch (error) {
      console.error('Failed to open thread:', error)
    }
  }

  const handleThreadClick = (messageId: string) => {
    handleReply(messageId)
  }

  return (
    <div className="flex flex-col h-full">
      {/* Messages Container */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {/* Load More Button */}
        {hasMore && (
          <div className="text-center">
            <button
              onClick={loadMoreMessages}
              disabled={loading}
              className="text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50"
            >
              {loading ? 'Loading...' : 'Load older messages'}
            </button>
          </div>
        )}

        {/* Messages */}
        {messages.map((message, index) => {
          const previousMessage = index > 0 ? messages[index - 1] : null
          const showDateSeparator = shouldShowDateSeparator(message, previousMessage)
          const isOwnMessage = message.authorId === user?.id

          return (
            <div key={message.id}>
              {/* Date Separator */}
              {showDateSeparator && (
                <div className="flex items-center justify-center my-4">
                  <div className="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full">
                    {formatDate(message.createdAt)}
                  </div>
                </div>
              )}

              {/* Message */}
              <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} relative group`}>
                <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
                  {!isOwnMessage && (
                    <div className="text-xs text-gray-700 mb-1 font-medium">
                      {message.author.name || message.author.username}
                    </div>
                  )}
                  <div
                    className={`px-4 py-2 rounded-lg ${
                      isOwnMessage
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-100 text-gray-900 border border-gray-200'
                    }`}
                  >
                    <div className="text-sm whitespace-pre-wrap">
                      {message.content.includes('```') ? (
                        <div>
                          {message.content.split('```').map((part, index) =>
                            index % 2 === 0 ? (
                              <span key={index}>{part}</span>
                            ) : (
                              <code key={index} className="block code-block code-dark text-green-400 p-2 rounded text-xs my-1 overflow-x-auto">
                                {part}
                              </code>
                            )
                          )}
                        </div>
                      ) : (
                        message.content
                      )}
                    </div>
                    <div className={`text-xs mt-1 ${isOwnMessage ? 'text-blue-100' : 'text-gray-600'}`}>
                      {formatTime(message.createdAt)}
                    </div>
                  </div>

                  {/* Thread Indicator */}
                  {message._count?.replies && message._count.replies > 0 && (
                    <ThreadIndicator
                      replyCount={message._count.replies}
                      onClick={() => handleThreadClick(message.id)}
                    />
                  )}
                </div>

                {/* Message Actions */}
                <MessageActions
                  message={message}
                  onReply={handleReply}
                  isOwnMessage={isOwnMessage}
                />
              </div>
            </div>
          )
        })}

        {/* Loading indicator */}
        {loading && messages.length === 0 && (
          <div className="text-center text-gray-500">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-2">Loading messages...</p>
          </div>
        )}

        {/* Empty state */}
        {!loading && messages.length === 0 && (
          <div className="text-center text-gray-500 py-8">
            <p>No messages yet. Start the conversation!</p>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="border-t border-gray-200 p-4">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded mb-3 text-sm">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 border border-gray-300 rounded-lg px-4 py-2 high-contrast-input focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500"
            disabled={sending}
            maxLength={2000}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || sending}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {sending ? 'Sending...' : 'Send'}
          </button>
        </form>
      </div>
    </div>
  )
}
