'use client'

import React, { useState } from 'react'
import { MessageWithAuthor } from '@/types'

interface MessageActionsProps {
  message: MessageWithAuthor
  onReply: (messageId: string) => void
  isOwnMessage: boolean
}

export default function MessageActions({ message, onReply, isOwnMessage }: MessageActionsProps) {
  const [showActions, setShowActions] = useState(false)

  const handleReply = () => {
    onReply(message.id)
    setShowActions(false)
  }

  return (
    <div 
      className="relative"
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Actions Trigger */}
      <div className={`absolute ${isOwnMessage ? 'left-0' : 'right-0'} top-0 transform -translate-y-1/2`}>
        {showActions && (
          <div className="flex items-center space-x-1 bg-white border border-gray-200 rounded-lg shadow-lg px-2 py-1">
            {/* Reply Button */}
            <button
              onClick={handleReply}
              className="p-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
              title="Reply in thread"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
              </svg>
            </button>

            {/* More Actions (for future features) */}
            <button
              className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors"
              title="More actions"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

// Thread indicator component for messages that have replies
interface ThreadIndicatorProps {
  replyCount: number
  onClick: () => void
}

export function ThreadIndicator({ replyCount, onClick }: ThreadIndicatorProps) {
  if (replyCount === 0) return null

  return (
    <button
      onClick={onClick}
      className="mt-2 flex items-center space-x-2 text-blue-600 hover:text-blue-700 text-sm transition-colors"
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
      </svg>
      <span>
        {replyCount} {replyCount === 1 ? 'reply' : 'replies'}
      </span>
    </button>
  )
}
