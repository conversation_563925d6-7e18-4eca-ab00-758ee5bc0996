'use client'

import React from 'react'
import { MessageWithAuthor } from '@/types'

interface MessageActionsProps {
  message: MessageWithAuthor
  onReply: (messageId: string) => void
  isOwnMessage: boolean
  className?: string
}

export default function MessageActions({ message, onReply, isOwnMessage, className = '' }: MessageActionsProps) {
  const handleReply = (e: React.MouseEvent) => {
    e.stopPropagation()
    onReply(message.id)
  }

  return (
    <div className={`message-actions ${className}`}>
      {/* Quick Reply Button - Always visible on mobile, hover on desktop */}
      <div className={`
        flex items-center space-x-1
        ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}
        opacity-0 group-hover:opacity-100
        md:opacity-100 lg:opacity-0 lg:group-hover:opacity-100
        transition-opacity duration-200
      `}>
        {/* Reply Button */}
        <button
          onClick={handleReply}
          className="
            p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50
            rounded-full transition-all duration-200
            hover:scale-110 active:scale-95
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
            shadow-sm hover:shadow-md
          "
          title="Reply in thread"
          aria-label={`Reply to message from ${message.author.name || message.author.username}`}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"
            />
          </svg>
        </button>

        {/* More Actions Button (for future features) */}
        <button
          className="
            p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50
            rounded-full transition-all duration-200
            hover:scale-110 active:scale-95
            focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50
            shadow-sm hover:shadow-md
          "
          title="More actions"
          aria-label="More message actions"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
            />
          </svg>
        </button>
      </div>
    </div>
  )
}

// Thread indicator component for messages that have replies
interface ThreadIndicatorProps {
  replyCount: number
  onClick: () => void
  className?: string
}

export function ThreadIndicator({ replyCount, onClick, className = '' }: ThreadIndicatorProps) {
  if (replyCount === 0) return null

  return (
    <button
      onClick={onClick}
      className={`
        mt-2 flex items-center space-x-2
        text-blue-600 hover:text-blue-700
        bg-blue-50 hover:bg-blue-100
        border border-blue-200 hover:border-blue-300
        rounded-lg px-3 py-1.5 text-sm
        transition-all duration-200
        hover:scale-105 active:scale-95
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
        shadow-sm hover:shadow-md
        ${className}
      `}
      aria-label={`View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'} in thread`}
    >
      <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
        />
      </svg>
      <span className="font-medium">
        {replyCount} {replyCount === 1 ? 'reply' : 'replies'}
      </span>
      <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
    </button>
  )
}
