'use client'

import React, { useState } from 'react'
import { GroupWithMembers } from '@/types'
import { ThreadProvider } from '@/contexts/ThreadContext'
import ChatInterface from '@/components/messages/ChatInterface'
import ThreadInterface from '@/components/threads/ThreadInterface'
import ThreadList from '@/components/threads/ThreadList'
import ThreadTester from '@/components/threads/ThreadTester'

interface ChatWithThreadsProps {
  group: GroupWithMembers
}

type ViewMode = 'chat' | 'thread' | 'thread-list'

export default function ChatWithThreads({ group }: ChatWithThreadsProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('chat')
  const [selectedThreadId, setSelectedThreadId] = useState<string | null>(null)

  const handleThreadOpen = (messageId: string) => {
    setSelectedThreadId(messageId)
    setViewMode('thread')
  }

  const handleThreadClose = () => {
    setViewMode('chat')
    setSelectedThreadId(null)
  }

  const handleShowThreadList = () => {
    setViewMode('thread-list')
  }

  const handleThreadSelect = (threadId: string) => {
    setSelectedThreadId(threadId)
    setViewMode('thread')
  }

  const handleBackToChat = () => {
    setViewMode('chat')
  }

  return (
    <ThreadProvider>
      <div className="flex h-full">
        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          {/* Header with Navigation */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
            <div className="flex items-center space-x-4">
              {viewMode !== 'chat' && (
                <button
                  onClick={handleBackToChat}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              )}
              
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  {viewMode === 'chat' && group.name}
                  {viewMode === 'thread' && 'Thread'}
                  {viewMode === 'thread-list' && 'All Threads'}
                </h2>
                {viewMode === 'chat' && (
                  <p className="text-sm text-gray-600">
                    {group.members.length} members
                  </p>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-2">
              {viewMode === 'chat' && (
                <button
                  onClick={handleShowThreadList}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
                  title="View all threads"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                </button>
              )}
              
              {/* Refresh Button */}
              <button
                className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
                title="Refresh"
                onClick={() => window.location.reload()}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>

              {/* Debug: Thread Tester (temporary) */}
              {process.env.NODE_ENV === 'development' && viewMode === 'chat' && (
                <details className="relative">
                  <summary className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100 cursor-pointer">
                    🧪
                  </summary>
                  <div className="absolute right-0 top-full mt-2 w-96 z-50">
                    <ThreadTester groupId={group.id} />
                  </div>
                </details>
              )}
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-hidden">
            {viewMode === 'chat' && (
              <ChatInterface 
                group={group} 
                onThreadOpen={handleThreadOpen}
              />
            )}
            
            {viewMode === 'thread' && (
              <ThreadInterface onClose={handleThreadClose} />
            )}
            
            {viewMode === 'thread-list' && (
              <ThreadList 
                groupId={group.id}
                onThreadSelect={handleThreadSelect}
                selectedThreadId={selectedThreadId}
              />
            )}
          </div>
        </div>

        {/* Thread Sidebar (Desktop only) */}
        <div className="hidden lg:block w-80 border-l border-gray-200">
          {viewMode === 'chat' && (
            <ThreadList 
              groupId={group.id}
              onThreadSelect={handleThreadSelect}
              selectedThreadId={selectedThreadId}
            />
          )}
          
          {viewMode === 'thread' && selectedThreadId && (
            <div className="h-full bg-gray-50 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-sm">Thread is open in main area</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </ThreadProvider>
  )
}

// Mobile Thread Navigation Component
interface MobileThreadNavProps {
  viewMode: ViewMode
  onModeChange: (mode: ViewMode) => void
  threadCount?: number
}

export function MobileThreadNav({ viewMode, onModeChange, threadCount = 0 }: MobileThreadNavProps) {
  return (
    <div className="lg:hidden border-t border-gray-200 bg-white">
      <div className="flex">
        <button
          onClick={() => onModeChange('chat')}
          className={`flex-1 py-3 px-4 text-center text-sm font-medium ${
            viewMode === 'chat'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Chat
        </button>
        
        <button
          onClick={() => onModeChange('thread-list')}
          className={`flex-1 py-3 px-4 text-center text-sm font-medium relative ${
            viewMode === 'thread-list'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Threads
          {threadCount > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {threadCount > 9 ? '9+' : threadCount}
            </span>
          )}
        </button>
      </div>
    </div>
  )
}
