'use client'

import React, { useState } from 'react'

interface ThreadTesterProps {
  groupId: string
}

export default function ThreadTester({ groupId }: ThreadTesterProps) {
  const [testResults, setTestResults] = useState<string[]>([])
  const [testing, setTesting] = useState(false)

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, result])
  }

  const runTests = async () => {
    setTesting(true)
    setTestResults([])
    
    try {
      // Test 1: Get main chat messages
      addResult('Testing: Get main chat messages...')
      const messagesResponse = await fetch(`/api/groups/${groupId}/messages`)
      if (messagesResponse.ok) {
        const messagesData = await messagesResponse.json()
        addResult(`✅ Main chat messages: ${messagesData.messages.length} messages found`)
      } else {
        addResult(`❌ Failed to get main chat messages: ${messagesResponse.status}`)
      }

      // Test 2: Get threads list
      addResult('Testing: Get threads list...')
      const threadsResponse = await fetch(`/api/groups/${groupId}/threads`)
      if (threadsResponse.ok) {
        const threadsData = await threadsResponse.json()
        addResult(`✅ Threads list: ${threadsData.threads.length} threads found`)
      } else {
        addResult(`❌ Failed to get threads list: ${threadsResponse.status}`)
      }

      // Test 3: Send a test message to main chat
      addResult('Testing: Send test message to main chat...')
      const sendMessageResponse = await fetch(`/api/groups/${groupId}/messages`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ content: 'Test message for thread testing' })
      })
      
      if (sendMessageResponse.ok) {
        const messageData = await sendMessageResponse.json()
        addResult(`✅ Test message sent: ${messageData.data.id}`)
        
        // Test 4: Create a thread reply to the test message
        addResult('Testing: Create thread reply...')
        const threadResponse = await fetch(`/api/messages/${messageData.data.id}/thread`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ content: 'This is a thread reply!' })
        })
        
        if (threadResponse.ok) {
          const threadData = await threadResponse.json()
          addResult(`✅ Thread reply created: ${threadData.data.id}`)
          
          // Test 5: Get thread messages
          addResult('Testing: Get thread messages...')
          const threadMessagesResponse = await fetch(`/api/messages/${messageData.data.id}/thread`)
          if (threadMessagesResponse.ok) {
            const threadMessagesData = await threadMessagesResponse.json()
            addResult(`✅ Thread messages: ${threadMessagesData.messages.length} messages in thread`)
          } else {
            addResult(`❌ Failed to get thread messages: ${threadMessagesResponse.status}`)
          }
        } else {
          addResult(`❌ Failed to create thread reply: ${threadResponse.status}`)
        }
      } else {
        addResult(`❌ Failed to send test message: ${sendMessageResponse.status}`)
      }

      addResult('🎉 All tests completed!')
      
    } catch (error) {
      addResult(`❌ Test error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className="p-4 bg-gray-50 border rounded-lg">
      <h3 className="font-semibold text-gray-900 mb-4">Thread Feature Tester</h3>
      
      <button
        onClick={runTests}
        disabled={testing}
        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50 mb-4"
      >
        {testing ? 'Running Tests...' : 'Run Thread Tests'}
      </button>

      {testResults.length > 0 && (
        <div className="bg-white border rounded p-3">
          <h4 className="font-medium text-gray-900 mb-2">Test Results:</h4>
          <div className="space-y-1 text-sm font-mono">
            {testResults.map((result, index) => (
              <div key={index} className="text-gray-700">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
