'use client'

import React, { useEffect } from 'react'
import { useThread } from '@/contexts/ThreadContext'
import { ThreadInfo } from '@/types'

interface ThreadListProps {
  groupId: string
  onThreadSelect: (threadId: string) => void
  selectedThreadId?: string | null
}

export default function ThreadList({ groupId, onThreadSelect, selectedThreadId }: ThreadListProps) {
  const { 
    threads, 
    threadsLoading, 
    loadThreads, 
    threadsHasMore, 
    threadsCurrentPage 
  } = useThread()

  useEffect(() => {
    if (groupId) {
      loadThreads(groupId)
    }
  }, [groupId])

  const loadMoreThreads = async () => {
    if (threadsHasMore && !threadsLoading) {
      await loadThreads(groupId, threadsCurrentPage + 1)
    }
  }

  const formatTime = (date: Date | string) => {
    const now = new Date()
    const messageDate = new Date(date)
    const diffInHours = Math.abs(now.getTime() - messageDate.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffInHours < 24 * 7) {
      return messageDate.toLocaleDateString([], { weekday: 'short' })
    } else {
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  const truncateContent = (content: string, maxLength: number = 60) => {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength) + '...'
  }

  if (threadsLoading && threads.length === 0) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-gray-200 h-16 rounded-lg"></div>
          ))}
        </div>
      </div>
    )
  }

  if (threads.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">
        <div className="mb-2">
          <svg className="w-12 h-12 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <p className="text-sm">No threads yet</p>
        <p className="text-xs text-gray-400 mt-1">Reply to a message to start a thread</p>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <h3 className="font-semibold text-gray-900">Threads</h3>
        <p className="text-sm text-gray-600">{threads.length} active threads</p>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="space-y-1 p-2">
          {threads.map((thread) => (
            <ThreadItem
              key={thread.id}
              thread={thread}
              isSelected={selectedThreadId === thread.id}
              onClick={() => onThreadSelect(thread.id)}
              formatTime={formatTime}
              truncateContent={truncateContent}
            />
          ))}
        </div>

        {/* Load More Button */}
        {threadsHasMore && (
          <div className="p-4 text-center border-t border-gray-100">
            <button
              onClick={loadMoreThreads}
              disabled={threadsLoading}
              className="text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50"
            >
              {threadsLoading ? 'Loading...' : 'Load more threads'}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

interface ThreadItemProps {
  thread: ThreadInfo
  isSelected: boolean
  onClick: () => void
  formatTime: (date: Date | string) => string
  truncateContent: (content: string, maxLength?: number) => string
}

function ThreadItem({ thread, isSelected, onClick, formatTime, truncateContent }: ThreadItemProps) {
  return (
    <div
      onClick={onClick}
      className={`p-3 rounded-lg cursor-pointer transition-colors ${
        isSelected 
          ? 'bg-blue-50 border border-blue-200' 
          : 'hover:bg-gray-50 border border-transparent'
      }`}
    >
      <div className="flex items-start space-x-3">
        <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
          {(thread.parentMessage.author.name || thread.parentMessage.author.username).charAt(0).toUpperCase()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <span className="font-medium text-gray-900 text-sm truncate">
              {thread.parentMessage.author.name || thread.parentMessage.author.username}
            </span>
            <span className="text-xs text-gray-500 flex-shrink-0 ml-2">
              {formatTime(thread.lastActivity)}
            </span>
          </div>
          
          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
            {truncateContent(thread.parentMessage.content)}
          </p>
          
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">
                {thread.messageCount} {thread.messageCount === 1 ? 'reply' : 'replies'}
              </span>
              {thread.participants.length > 1 && (
                <>
                  <span className="text-xs text-gray-300">•</span>
                  <div className="flex -space-x-1">
                    {thread.participants.slice(0, 3).map((participant, index) => (
                      <div
                        key={participant.id}
                        className="w-4 h-4 bg-gray-400 rounded-full border border-white text-xs flex items-center justify-center text-white"
                        style={{ zIndex: 3 - index }}
                      >
                        {(participant.name || participant.username).charAt(0).toUpperCase()}
                      </div>
                    ))}
                    {thread.participants.length > 3 && (
                      <div className="w-4 h-4 bg-gray-300 rounded-full border border-white text-xs flex items-center justify-center text-gray-600">
                        +
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
            
            {isSelected && (
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
