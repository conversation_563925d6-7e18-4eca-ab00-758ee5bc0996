'use client'

import React, { useState, useRef, useEffect } from 'react'
import { useThread } from '@/contexts/ThreadContext'
import { useAuth } from '@/contexts/AuthContext'
import { MessageWithAuthor } from '@/types'

interface ThreadInterfaceProps {
  onClose: () => void
}

export default function ThreadInterface({ onClose }: ThreadInterfaceProps) {
  const { 
    currentThread, 
    threadMessages, 
    threadLoading, 
    threadSending, 
    sendThreadMessage,
    currentThreadId,
    loadThreadMessages,
    threadMessagesHasMore,
    threadMessagesCurrentPage
  } = useThread()
  const { user } = useAuth()
  const [newMessage, setNewMessage] = useState('')
  const [error, setError] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    scrollToBottom()
  }, [threadMessages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newMessage.trim() || threadSending || !currentThreadId) return

    setError('')
    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      await sendThreadMessage(currentThreadId, messageContent)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message')
      setNewMessage(messageContent) // Restore message on error
    }
  }

  const loadMoreMessages = async () => {
    if (threadMessagesHasMore && !threadLoading && currentThreadId) {
      await loadThreadMessages(currentThreadId, threadMessagesCurrentPage + 1)
    }
  }

  const formatTime = (date: string | Date) => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString()
  }

  const shouldShowDateSeparator = (message: MessageWithAuthor, previousMessage: MessageWithAuthor | null) => {
    if (!previousMessage) return true
    
    const messageDate = new Date(message.createdAt).toDateString()
    const previousDate = new Date(previousMessage.createdAt).toDateString()
    
    return messageDate !== previousDate
  }

  if (!currentThread) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-500">Select a thread to view messages</div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-full bg-white border-l border-gray-200">
      {/* Thread Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">Thread</h3>
          <p className="text-sm text-gray-600 truncate">
            Reply to {currentThread.parentMessage.author.name || currentThread.parentMessage.author.username}
          </p>
          <p className="text-xs text-gray-500">
            {currentThread.messageCount} messages • {currentThread.participants.length} participants
          </p>
        </div>
        <button
          onClick={onClose}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Parent Message */}
      <div className="p-4 bg-blue-50 border-b border-blue-100">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {(currentThread.parentMessage.author.name || currentThread.parentMessage.author.username).charAt(0).toUpperCase()}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <span className="font-medium text-gray-900">
                {currentThread.parentMessage.author.name || currentThread.parentMessage.author.username}
              </span>
              <span className="text-xs text-gray-500">
                {formatTime(currentThread.parentMessage.createdAt)}
              </span>
            </div>
            <p className="text-gray-700 mt-1">{currentThread.parentMessage.content}</p>
          </div>
        </div>
      </div>

      {/* Thread Messages */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {/* Load More Button */}
        {threadMessagesHasMore && (
          <div className="text-center">
            <button
              onClick={loadMoreMessages}
              disabled={threadLoading}
              className="text-blue-500 hover:text-blue-600 text-sm disabled:opacity-50"
            >
              {threadLoading ? 'Loading...' : 'Load older messages'}
            </button>
          </div>
        )}

        {/* Thread Messages */}
        {threadMessages.filter(msg => msg.id !== currentThread.parentMessage.id).map((message, index) => {
          const previousMessage = index > 0 ? threadMessages[index - 1] : null
          const showDateSeparator = shouldShowDateSeparator(message, previousMessage)
          const isOwnMessage = message.authorId === user?.id

          return (
            <div key={message.id}>
              {showDateSeparator && (
                <div className="flex items-center justify-center my-4">
                  <div className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                    {formatDate(message.createdAt)}
                  </div>
                </div>
              )}
              
              <div className={`flex items-start space-x-3 ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
                <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {(message.author.name || message.author.username).charAt(0).toUpperCase()}
                </div>
                <div className={`flex-1 min-w-0 ${isOwnMessage ? 'text-right' : ''}`}>
                  <div className={`flex items-center space-x-2 ${isOwnMessage ? 'flex-row-reverse space-x-reverse' : ''}`}>
                    <span className="font-medium text-gray-900">
                      {message.author.name || message.author.username}
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatTime(message.createdAt)}
                    </span>
                  </div>
                  <div className={`mt-1 ${isOwnMessage ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-900'} rounded-lg px-3 py-2 inline-block max-w-xs break-words`}>
                    {message.content}
                  </div>
                </div>
              </div>
            </div>
          )
        })}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 bg-white">
        {error && (
          <div className="mb-2 text-sm text-red-600 bg-red-50 border border-red-200 rounded px-3 py-2">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSendMessage} className="flex space-x-2">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Reply to thread..."
            className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:text-gray-500"
            disabled={threadSending}
            maxLength={2000}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || threadSending}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {threadSending ? 'Sending...' : 'Send'}
          </button>
        </form>
      </div>
    </div>
  )
}
