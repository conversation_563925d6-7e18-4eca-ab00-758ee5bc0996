import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'

// GET /api/groups/[groupId]/threads - Get all threads in a group
export async function GET(request: NextRequest, { params }: { params: Promise<{ groupId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { groupId } = await params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = (page - 1) * limit

    // Check if user is member of the group
    const groupMember = await prisma.groupMember.findFirst({
      where: {
        groupId: groupId,
        userId: user.id
      }
    })

    if (!groupMember) {
      return NextResponse.json(
        { error: 'Access denied. You are not a member of this group.' },
        { status: 403 }
      )
    }

    // Get all thread starter messages (messages that have replies)
    const threadStarters = await prisma.message.findMany({
      where: {
        groupId: groupId,
        isThreadStarter: true
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        _count: {
          select: {
            replies: true
          }
        }
      },
      orderBy: {
        updatedAt: 'desc' // Order by last activity
      },
      take: limit,
      skip: offset
    })

    // Get the latest message in each thread for last activity info
    const threadsWithLastActivity = await Promise.all(
      threadStarters.map(async (starter) => {
        const lastMessage = await prisma.message.findFirst({
          where: {
            threadId: starter.id
          },
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            author: {
              select: {
                id: true,
                username: true,
                name: true,
                avatar: true,
              }
            }
          }
        })

        // Get unique participants in this thread
        const participants = await prisma.user.findMany({
          where: {
            messages: {
              some: {
                OR: [
                  { id: starter.id },
                  { threadId: starter.id }
                ]
              }
            }
          },
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          },
          take: 5 // Limit to first 5 participants for performance
        })

        return {
          id: starter.id,
          parentMessage: starter,
          messageCount: starter._count.replies + 1, // +1 for the starter message
          lastActivity: lastMessage?.createdAt || starter.createdAt,
          lastMessage: lastMessage,
          participants
        }
      })
    )

    // Get total count for pagination
    const totalThreads = await prisma.message.count({
      where: {
        groupId: groupId,
        isThreadStarter: true
      }
    })

    const totalPages = Math.ceil(totalThreads / limit)
    const hasMore = page < totalPages

    return NextResponse.json({
      threads: threadsWithLastActivity,
      pagination: {
        page,
        limit,
        totalThreads,
        totalPages,
        hasMore
      }
    })
  } catch (error) {
    console.error('Get threads error:', error)
    return NextResponse.json(
      { error: 'Failed to get threads' },
      { status: 500 }
    )
  }
}
