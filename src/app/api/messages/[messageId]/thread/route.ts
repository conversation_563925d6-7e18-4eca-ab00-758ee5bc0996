import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getAuthenticatedUser } from '@/lib/middleware'
import { z } from 'zod'

const createThreadMessageSchema = z.object({
  content: z.string().min(1, 'Message content is required').max(2000, 'Message too long'),
})

// GET /api/messages/[messageId]/thread - Get thread messages
export async function GET(request: NextRequest, { params }: { params: Promise<{ messageId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { messageId } = await params
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = (page - 1) * limit

    // Get the parent message to verify access
    const parentMessage = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        },
        group: {
          include: {
            members: {
              where: { userId: user.id }
            }
          }
        }
      }
    })

    if (!parentMessage) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 })
    }

    // Check if user is member of the group
    if (parentMessage.group.members.length === 0) {
      return NextResponse.json(
        { error: 'Access denied. You are not a member of this group.' },
        { status: 403 }
      )
    }

    // Get thread messages
    const threadMessages = await prisma.message.findMany({
      where: {
        OR: [
          { id: messageId }, // Include the parent message
          { threadId: messageId } // Include all thread replies
        ]
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      },
      take: limit,
      skip: offset
    })

    // Get total count for pagination
    const totalMessages = await prisma.message.count({
      where: {
        OR: [
          { id: messageId },
          { threadId: messageId }
        ]
      }
    })

    const totalPages = Math.ceil(totalMessages / limit)
    const hasMore = page < totalPages

    // Get thread participants
    const participants = await prisma.user.findMany({
      where: {
        messages: {
          some: {
            OR: [
              { id: messageId },
              { threadId: messageId }
            ]
          }
        }
      },
      select: {
        id: true,
        username: true,
        name: true,
        avatar: true,
      }
    })

    return NextResponse.json({
      parentMessage,
      messages: threadMessages,
      participants,
      pagination: {
        page,
        limit,
        totalMessages,
        totalPages,
        hasMore
      }
    })
  } catch (error) {
    console.error('Get thread messages error:', error)
    return NextResponse.json(
      { error: 'Failed to get thread messages' },
      { status: 500 }
    )
  }
}

// POST /api/messages/[messageId]/thread - Reply to a message (create thread message)
export async function POST(request: NextRequest, { params }: { params: Promise<{ messageId: string }> }) {
  try {
    const user = await getAuthenticatedUser(request)
    if (!user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
    }

    const { messageId } = await params
    const body = await request.json()
    const { content } = createThreadMessageSchema.parse(body)

    // Get the parent message to verify access and get group info
    const parentMessage = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        group: {
          include: {
            members: {
              where: { userId: user.id }
            }
          }
        }
      }
    })

    if (!parentMessage) {
      return NextResponse.json({ error: 'Message not found' }, { status: 404 })
    }

    // Check if user is member of the group
    if (parentMessage.group.members.length === 0) {
      return NextResponse.json(
        { error: 'Access denied. You are not a member of this group.' },
        { status: 403 }
      )
    }

    // Create thread message
    const threadMessage = await prisma.message.create({
      data: {
        content,
        authorId: user.id,
        groupId: parentMessage.groupId,
        parentMessageId: messageId,
        threadId: messageId, // Use parent message ID as thread ID
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            name: true,
            avatar: true,
          }
        }
      }
    })

    // Update parent message to mark it as thread starter if it's the first reply
    if (!parentMessage.isThreadStarter) {
      await prisma.message.update({
        where: { id: messageId },
        data: { isThreadStarter: true }
      })
    }

    return NextResponse.json({
      message: 'Thread message sent successfully',
      data: threadMessage
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }

    console.error('Send thread message error:', error)
    return NextResponse.json(
      { error: 'Failed to send thread message' },
      { status: 500 }
    )
  }
}
