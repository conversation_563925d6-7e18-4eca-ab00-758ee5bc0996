@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Code block styling improvements */
.code-block {
  font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  line-height: 1.5;
  tab-size: 2;
}

/* Improved contrast for accessibility */
.high-contrast-input {
  color: #1f2937 !important;
  background-color: #ffffff !important;
}

.high-contrast-input::placeholder {
  color: #6b7280 !important;
  opacity: 1;
}

/* Dark theme code blocks */
.code-dark {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border: 1px solid #334155;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Syntax highlighting colors */
.syntax-keyword { color: #f59e0b; }
.syntax-string { color: #10b981; }
.syntax-comment { color: #6b7280; font-style: italic; }
.syntax-number { color: #3b82f6; }

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Message hover effects */
.message-actions {
  transition: opacity 0.2s ease-in-out;
}

/* Ensure smooth hover transitions */
.group:hover .message-actions {
  opacity: 1;
}

/* Mobile-first approach for message actions */
@media (max-width: 768px) {
  .message-actions {
    opacity: 1 !important;
  }
}

/* Focus states for accessibility */
.message-actions button:focus {
  opacity: 1;
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
