# MyBinder - Group Chat dengan Note-Taking Terintegrasi

MyBinder adalah aplikasi web modern yang menggabungkan fitur group chat dan note-taking dalam satu platform. Aplikasi ini memungkinkan tim untuk berkomunikasi dan berkolaborasi dengan mudah melalui sistem pesan real-time dan catatan berbasis blok yang fleksibel.

## 🚀 Fitur Utama

### 💬 Group Chat
- **Pesan Real-time**: Komunikasi instan dengan anggota grup
- **Manajemen Grup**: <PERSON><PERSON><PERSON>, kelo<PERSON>, dan bergabung dengan grup
- **Role-based Access**: Sistem peran (Owner, Admin, Member) dengan hak akses berbeda
- **Riwayat Pesan**: Simpan dan akses riwayat percakapan

### 📝 Block-based Notes
- **Editor Fleksibel**: Sistem catatan berbasis blok seperti Notion
- **Berbagai Tipe Blok**: Text, Heading, List, Code, dan lainnya
- **Kolaborasi Real-time**: Edit catatan bersama anggota grup
- **Organisasi Catatan**: Kelola catatan per grup dengan mudah

### 🔐 Keamanan
- **Autentikasi JWT**: Sistem login yang aman dengan HTTP-only cookies
- **Password Hashing**: Enkripsi password menggunakan bcrypt
- **Role-based Authorization**: Kontrol akses berdasarkan peran pengguna

## 🛠️ Teknologi yang Digunakan

- **Frontend**: Next.js 15.5.3 dengan TypeScript
- **Styling**: Tailwind CSS
- **Database**: PostgreSQL dengan Prisma ORM
- **Authentication**: JWT dengan bcryptjs
- **Validation**: Zod untuk validasi data
- **Testing**: Jest dengan React Testing Library
- **Deployment**: Vercel (Frontend) + Supabase (Database)

## 📋 Prasyarat

Sebelum menjalankan aplikasi, pastikan Anda memiliki:

- Node.js 18+
- Docker dan Docker Compose
- Git

## 🚀 Instalasi dan Setup Lokal

### 1. Clone Repository
```bash
git clone <repository-url>
cd mybinder
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Setup Environment Variables
```bash
cp .env.example .env.local
```

Edit file `.env.local` dan sesuaikan dengan konfigurasi Anda:
```env
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/mybinder"

# JWT Secret
JWT_SECRET="your-super-secret-jwt-key-here"

# Next.js
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Setup Database dengan Docker
```bash
# Jalankan PostgreSQL container
docker-compose up -d

# Generate Prisma client
npx prisma generate

# Jalankan migrasi database
npx prisma db push

# Seed database dengan data demo
npm run db:seed
```

### 5. Jalankan Development Server
```bash
npm run dev
```

Buka [http://localhost:3000](http://localhost:3000) di browser Anda.

## 🧪 Testing

Jalankan test suite lengkap:
```bash
# Jalankan semua test
npm test

# Jalankan test dengan coverage
npm run test:coverage

# Jalankan test dalam watch mode
npm run test:watch
```

## 👥 Akun Demo

Aplikasi dilengkapi dengan 2 akun demo untuk evaluasi:

### Demo User 1
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Role**: Owner dari grup "Demo Team"

### Demo User 2
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Role**: Member dari grup "Demo Team"

## 📁 Struktur Project

```
mybinder/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── api/            # API routes
│   │   ├── dashboard/      # Dashboard pages
│   │   └── layout.tsx      # Root layout
│   ├── components/         # React components
│   │   ├── auth/          # Authentication components
│   │   ├── groups/        # Group management components
│   │   ├── messages/      # Chat components
│   │   └── notes/         # Note-taking components
│   ├── lib/               # Utility libraries
│   │   ├── auth.ts        # Authentication utilities
│   │   ├── prisma.ts      # Database client
│   │   └── utils.ts       # General utilities
│   └── __tests__/         # Test files
├── prisma/                # Database schema dan migrations
├── docker-compose.yml     # Docker configuration
└── README.md             # Dokumentasi ini
```

## 🚀 Deployment ke Production

### Persiapan Deployment

1. **Setup Supabase Database**
   - Buat project baru di [Supabase](https://supabase.com)
   - Dapatkan connection string dari Settings > Database
   - Update `DATABASE_URL` di environment variables

2. **Setup Vercel**
   - Push code ke GitHub repository
   - Connect repository ke [Vercel](https://vercel.com)
   - Configure environment variables di Vercel dashboard

### Environment Variables untuk Production

```env
# Database (Supabase)
DATABASE_URL="postgresql://postgres:[password]@[host]:5432/postgres"

# JWT Secret (generate random string)
JWT_SECRET="your-production-jwt-secret"

# Next.js
NEXTAUTH_SECRET="your-production-nextauth-secret"
NEXTAUTH_URL="https://your-app.vercel.app"
```

### Deploy ke Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

## 📚 API Documentation

### Authentication Endpoints

#### POST `/api/auth/login`
Login pengguna dengan email dan password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "username": "username",
    "name": "User Name"
  },
  "token": "jwt-token"
}
```

#### POST `/api/auth/register`
Registrasi pengguna baru.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "name": "User Name"
}
```

### Group Management Endpoints

#### GET `/api/groups`
Mendapatkan daftar grup pengguna.

#### POST `/api/groups`
Membuat grup baru.

**Request Body:**
```json
{
  "name": "Group Name",
  "description": "Group Description"
}
```

#### GET `/api/groups/[groupId]`
Mendapatkan detail grup dan pesan.

#### POST `/api/groups/[groupId]/messages`
Mengirim pesan ke grup.

**Request Body:**
```json
{
  "content": "Message content"
}
```

### Notes Endpoints

#### GET `/api/groups/[groupId]/notes`
Mendapatkan daftar catatan grup.

#### POST `/api/groups/[groupId]/notes`
Membuat catatan baru.

**Request Body:**
```json
{
  "title": "Note Title",
  "blocks": [
    {
      "type": "text",
      "content": "Note content"
    }
  ]
}
```

## 🔧 Development Commands

```bash
# Development
npm run dev              # Start development server
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:generate     # Generate Prisma client
npm run db:push         # Push schema to database
npm run db:migrate      # Run database migrations
npm run db:seed         # Seed database with demo data
npm run db:studio       # Open Prisma Studio

# Testing
npm test                # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage

# Linting & Formatting
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint errors
npm run format          # Format code with Prettier
```

## 🤝 Contributing

1. Fork repository
2. Buat feature branch (`git checkout -b feature/amazing-feature`)
3. Commit perubahan (`git commit -m 'Add amazing feature'`)
4. Push ke branch (`git push origin feature/amazing-feature`)
5. Buat Pull Request

## 📝 License

Project ini menggunakan MIT License. Lihat file `LICENSE` untuk detail lengkap.

## 🆘 Troubleshooting

### Database Connection Issues
```bash
# Restart Docker containers
docker-compose down
docker-compose up -d

# Reset database
npx prisma db push --force-reset
npm run db:seed
```

### Port Already in Use
```bash
# Kill process on port 3000
npx kill-port 3000

# Or use different port
npm run dev -- -p 3001
```

### Environment Variables Not Loading
- Pastikan file `.env.local` ada di root directory
- Restart development server setelah mengubah environment variables
- Periksa nama variable sesuai dengan yang digunakan di kode

## 📞 Support

Jika Anda mengalami masalah atau memiliki pertanyaan:

1. Periksa [Issues](https://github.com/your-repo/issues) yang sudah ada
2. Buat issue baru dengan detail lengkap
3. Sertakan log error dan langkah reproduksi

---

**Dibuat dengan ❤️ menggunakan Next.js dan TypeScript**
