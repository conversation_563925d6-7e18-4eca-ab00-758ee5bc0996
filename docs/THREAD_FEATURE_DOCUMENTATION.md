# Thread Feature Documentation

## Overview

Fitur thread memungkinkan pengguna untuk membalas pesan tertentu dan membuat percakapan terpisah dari chat utama. Implementasi ini menggunakan self-referential relationship pada model Message untuk efisiensi dan fleksibilitas.

## Database Schema

### Message Model Updates

```prisma
model Message {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Thread support fields
  parentMessageId String?  // ID of parent message (null for main chat messages)
  threadId        String?  // ID of thread (same as parentMessageId for all messages in a thread)
  isThreadStarter Boolean @default(false) // Marks the message that starts a thread

  // Relations
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)
  authorId String
  group    Group  @relation(fields: [groupId], references: [id], onDelete: Cascade)
  groupId  String

  // Thread relations
  parentMessage Message? @relation("MessageThread", fields: [parentMessageId], references: [id], onDelete: Cascade)
  replies       Message[] @relation("MessageThread")

  @@index([groupId, parentMessageId]) // For efficient thread queries
  @@index([threadId]) // For efficient thread message queries
  @@map("messages")
}
```

## API Endpoints

### 1. Create Thread Reply
**POST** `/api/messages/[messageId]/thread`

Creates a new reply in a thread.

**Request Body:**
```json
{
  "content": "Thread reply content"
}
```

**Response:**
```json
{
  "message": "Thread message sent successfully",
  "data": {
    "id": "message_id",
    "content": "Thread reply content",
    "parentMessageId": "parent_message_id",
    "threadId": "thread_id",
    "author": { ... }
  }
}
```

### 2. Get Thread Messages
**GET** `/api/messages/[messageId]/thread`

Retrieves all messages in a thread.

**Query Parameters:**
- `page` (optional): Page number for pagination
- `limit` (optional): Number of messages per page

**Response:**
```json
{
  "parentMessage": { ... },
  "messages": [ ... ],
  "participants": [ ... ],
  "pagination": { ... }
}
```

### 3. Get Group Threads
**GET** `/api/groups/[groupId]/threads`

Retrieves all threads in a group.

**Query Parameters:**
- `page` (optional): Page number for pagination
- `limit` (optional): Number of threads per page

**Response:**
```json
{
  "threads": [
    {
      "id": "thread_id",
      "parentMessage": { ... },
      "messageCount": 5,
      "lastActivity": "2025-09-24T13:09:21.683Z",
      "participants": [ ... ]
    }
  ],
  "pagination": { ... }
}
```

## Frontend Components

### 1. ChatWithThreads
Main component that orchestrates chat and thread functionality.

**Features:**
- Responsive design (desktop sidebar, mobile navigation)
- Thread navigation
- Error boundary protection

### 2. ThreadInterface
Displays thread messages and allows replies.

**Features:**
- Parent message display
- Thread message list
- Reply input
- Loading and error states

### 3. ThreadList
Shows all active threads in a group.

**Features:**
- Thread preview with last activity
- Participant avatars
- Message count indicators

### 4. MessageActions
Hover-activated action buttons for messages.

**Features:**
- Reply button with hover effect
- Responsive design (always visible on mobile)
- Accessibility support

### 5. ThreadIndicator
Shows reply count for messages with threads.

**Features:**
- Click to open thread
- Visual thread indicator
- Responsive design

## Context Management

### ThreadContext
Manages thread state and operations.

**State:**
- `threads`: List of threads in current group
- `currentThread`: Currently open thread
- `threadMessages`: Messages in current thread
- Loading and pagination states

**Operations:**
- `loadThreads()`: Load threads for a group
- `openThread()`: Open a specific thread
- `sendThreadMessage()`: Send reply to thread
- `closeThread()`: Close current thread

## User Experience Features

### 1. Hover Effects
- Reply buttons appear on message hover (desktop)
- Always visible on mobile for accessibility
- Smooth transitions and animations

### 2. Visual Indicators
- Thread count badges on messages with replies
- Clear distinction between main chat and thread messages
- Loading states and error handling

### 3. Navigation
- Easy switching between main chat and threads
- Thread list sidebar on desktop
- Mobile-friendly navigation tabs

### 4. Accessibility
- ARIA labels for screen readers
- Keyboard navigation support
- Focus management
- High contrast support

## Implementation Details

### Database Queries
- Main chat messages: `WHERE parentMessageId IS NULL`
- Thread messages: `WHERE threadId = 'thread_id'`
- Thread starters: `WHERE isThreadStarter = true`

### Performance Optimizations
- Indexed queries for efficient thread retrieval
- Pagination for large thread lists
- Lazy loading of thread content

### Error Handling
- API error responses with proper HTTP status codes
- Frontend error boundaries
- Graceful degradation for network issues

## Testing

### Manual Testing Steps
1. Login with demo account (<EMAIL> / demo123)
2. Navigate to a group with messages
3. Hover over a message to see reply button
4. Click reply to start a thread
5. Send replies in the thread
6. Navigate between main chat and thread
7. Test thread list functionality

### API Testing
Use the ThreadTester component (development mode only) to test all API endpoints automatically.

## Future Enhancements

1. **Real-time Updates**: WebSocket integration for live thread updates
2. **Thread Notifications**: Notify users of new thread activity
3. **Thread Search**: Search within thread messages
4. **Thread Permissions**: Control who can reply to threads
5. **Thread Archiving**: Archive old or resolved threads
6. **Nested Threads**: Support for replies to thread messages

## Troubleshooting

### Common Issues

1. **Thread not opening**: Check authentication and group membership
2. **Messages not loading**: Verify API endpoints and database connection
3. **Hover effects not working**: Check CSS classes and responsive breakpoints
4. **Performance issues**: Review database indexes and query optimization

### Debug Tools

- ThreadTester component for API testing
- Browser developer tools for frontend debugging
- Server logs for backend issues
- Database query analysis for performance

## Security Considerations

- Authentication required for all thread operations
- Group membership validation
- Input sanitization and validation
- Rate limiting on API endpoints
- XSS protection in message content
