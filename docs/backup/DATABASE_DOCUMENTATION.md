# Dokumentasi Database MyBinder

## Ringkasan

MyBinder menggunakan PostgreSQL sebagai database utama dengan Prisma ORM untuk type-safe database operations. Database dirancang untuk mendukung fitur group chat, note-taking kolaboratif, dan manajemen user dengan struktur relational yang optimal.

## 🗄️ Database yang Digunakan: PostgreSQL

### Mengapa PostgreSQL?

PostgreSQL dipilih sebagai database utama karena beberapa alasan strategis:

#### Kelebihan PostgreSQL

1. **ACID Compliance**: Menjamin konsistensi data untuk transaksi critical
2. **JSON Support**: Native JSON/JSONB untuk data semi-structured
3. **Advanced Features**: Window functions, CTEs, full-text search
4. **Extensibility**: Custom functions, extensions, dan data types
5. **Performance**: Excellent query optimization dan indexing
6. **Reliability**: Battle-tested dalam production environments
7. **Open Source**: No licensing costs, community support

#### Fitur PostgreSQL yang Dimanfaatkan

```sql
-- JSON/JSONB untuk metadata fleksibel
ALTER TABLE users ADD COLUMN preferences JSONB;

-- Full-text search untuk pencarian pesan
CREATE INDEX idx_messages_content_fts 
ON messages USING gin(to_tsvector('english', content));

-- Partial indexes untuk performance
CREATE INDEX idx_active_groups 
ON groups (created_at) WHERE is_private = false;

-- Advanced constraints
ALTER TABLE group_members 
ADD CONSTRAINT unique_user_group UNIQUE (user_id, group_id);
```

### Perbandingan dengan Alternatif

| Database | ACID | JSON | Scaling | Learning Curve | Ecosystem | Skor |
|----------|------|------|---------|----------------|-----------|------|
| **PostgreSQL** ✅ | ✅ | ✅ | Vertical++ | Medium | Excellent | 9/10 |
| **MySQL** | ✅ | Limited | Vertical+ | Easy | Good | 7/10 |
| **MongoDB** | Limited | ✅ | Horizontal++ | Medium | Good | 6/10 |
| **SQLite** | ✅ | Limited | Single-user | Easy | Limited | 5/10 |

## 🏗️ Database Schema Design

### Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o{ GroupMember : "belongs to"
    User ||--o{ Group : "owns"
    User ||--o{ Message : "authors"
    User ||--o{ Note : "creates"
    User ||--o{ NoteBlock : "authors"
    
    Group ||--o{ GroupMember : "has"
    Group ||--o{ Message : "contains"
    Group ||--o{ Note : "contains"
    
    Note ||--o{ NoteBlock : "contains"
    
    User {
        string id PK
        string email UK
        string username UK
        string password
        string name
        string avatar
        datetime created_at
        datetime updated_at
    }
    
    Group {
        string id PK
        string name
        string description
        string avatar
        boolean is_private
        string owner_id FK
        datetime created_at
        datetime updated_at
    }
    
    GroupMember {
        string id PK
        string user_id FK
        string group_id FK
        enum role
        datetime joined_at
    }
    
    Message {
        string id PK
        string content
        string author_id FK
        string group_id FK
        datetime created_at
        datetime updated_at
    }
    
    Note {
        string id PK
        string title
        string description
        string author_id FK
        string group_id FK
        datetime created_at
        datetime updated_at
    }
    
    NoteBlock {
        string id PK
        enum type
        string content
        integer order
        string note_id FK
        string author_id FK
        datetime created_at
        datetime updated_at
    }
```

### Tabel dan Struktur

#### 1. Users Table
```sql
CREATE TABLE users (
    id VARCHAR(25) PRIMARY KEY DEFAULT cuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    avatar VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_created_at ON users(created_at);
```

**Design Decisions:**
- **CUID untuk ID**: Collision-resistant, sortable, URL-safe
- **Email + Username Unique**: Fleksibilitas login dengan email atau username
- **Avatar URL**: Mendukung external avatar services
- **Timestamps**: Audit trail untuk user creation dan updates

#### 2. Groups Table
```sql
CREATE TABLE groups (
    id VARCHAR(25) PRIMARY KEY DEFAULT cuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    avatar VARCHAR(500),
    is_private BOOLEAN DEFAULT FALSE,
    owner_id VARCHAR(25) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_groups_owner_id ON groups(owner_id);
CREATE INDEX idx_groups_is_private ON groups(is_private);
CREATE INDEX idx_groups_created_at ON groups(created_at);
```

**Design Decisions:**
- **Cascade Delete**: Jika owner dihapus, grup ikut terhapus
- **Privacy Flag**: Mendukung public dan private groups
- **Flexible Description**: TEXT untuk deskripsi panjang

#### 3. Group Members Table
```sql
CREATE TABLE group_members (
    id VARCHAR(25) PRIMARY KEY DEFAULT cuid(),
    user_id VARCHAR(25) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    group_id VARCHAR(25) NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    role VARCHAR(10) DEFAULT 'MEMBER' CHECK (role IN ('OWNER', 'ADMIN', 'MEMBER')),
    joined_at TIMESTAMP DEFAULT NOW(),
    
    UNIQUE(user_id, group_id)
);

-- Indexes
CREATE INDEX idx_group_members_user_id ON group_members(user_id);
CREATE INDEX idx_group_members_group_id ON group_members(group_id);
CREATE INDEX idx_group_members_role ON group_members(role);
```

**Design Decisions:**
- **Many-to-Many Relationship**: User bisa join multiple groups
- **Role-based Access**: OWNER, ADMIN, MEMBER dengan permissions berbeda
- **Unique Constraint**: Prevent duplicate memberships
- **Joined Timestamp**: Track kapan user join grup

#### 4. Messages Table
```sql
CREATE TABLE messages (
    id VARCHAR(25) PRIMARY KEY DEFAULT cuid(),
    content TEXT NOT NULL,
    author_id VARCHAR(25) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    group_id VARCHAR(25) NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_messages_group_id_created_at ON messages(group_id, created_at DESC);
CREATE INDEX idx_messages_author_id ON messages(author_id);

-- Full-text search
CREATE INDEX idx_messages_content_fts ON messages 
USING gin(to_tsvector('english', content));
```

**Design Decisions:**
- **Composite Index**: Optimized untuk pagination per group
- **Full-text Search**: Pencarian pesan dengan PostgreSQL FTS
- **No Soft Delete**: Messages dihapus permanent untuk privacy

#### 5. Notes Table
```sql
CREATE TABLE notes (
    id VARCHAR(25) PRIMARY KEY DEFAULT cuid(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    author_id VARCHAR(25) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    group_id VARCHAR(25) NOT NULL REFERENCES groups(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_notes_group_id ON notes(group_id);
CREATE INDEX idx_notes_author_id ON notes(author_id);
CREATE INDEX idx_notes_updated_at ON notes(updated_at DESC);
```

#### 6. Note Blocks Table
```sql
CREATE TABLE note_blocks (
    id VARCHAR(25) PRIMARY KEY DEFAULT cuid(),
    type VARCHAR(20) DEFAULT 'TEXT' CHECK (type IN (
        'TEXT', 'HEADING_1', 'HEADING_2', 'HEADING_3',
        'BULLET_LIST', 'NUMBERED_LIST', 'CODE', 'QUOTE'
    )),
    content TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    note_id VARCHAR(25) NOT NULL REFERENCES notes(id) ON DELETE CASCADE,
    author_id VARCHAR(25) NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_note_blocks_note_id_order ON note_blocks(note_id, "order");
CREATE INDEX idx_note_blocks_author_id ON note_blocks(author_id);
```

**Design Decisions:**
- **Block-based Architecture**: Flexible content structure seperti Notion
- **Order Field**: Maintain sequence blocks dalam note
- **Type Enum**: Predefined block types untuk consistency
- **Author Tracking**: Track siapa yang membuat setiap block

## 🔧 Prisma ORM Integration

### Schema Definition

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ownedGroups    Group[]       @relation("GroupOwner")
  memberships    GroupMember[]
  messages       Message[]
  notes          Note[]
  noteBlocks     NoteBlock[]

  @@map("users")
}
```

### Type-Safe Queries

```typescript
// Complex query dengan type safety
const getUserWithGroups = async (userId: string) => {
  return await prisma.user.findUnique({
    where: { id: userId },
    include: {
      memberships: {
        include: {
          group: {
            include: {
              _count: {
                select: {
                  members: true,
                  messages: true,
                  notes: true
                }
              }
            }
          }
        }
      }
    }
  })
}

// Type inference otomatis
type UserWithGroups = Prisma.PromiseReturnType<typeof getUserWithGroups>
```

### Migration Strategy

```bash
# Generate migration
npx prisma migrate dev --name add_user_preferences

# Deploy to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset
```

## 📊 Performance Optimizations

### Indexing Strategy

#### 1. Primary Indexes
```sql
-- Composite indexes untuk queries umum
CREATE INDEX idx_messages_group_created ON messages(group_id, created_at DESC);
CREATE INDEX idx_group_members_user_group ON group_members(user_id, group_id);
CREATE INDEX idx_note_blocks_note_order ON note_blocks(note_id, "order");
```

#### 2. Partial Indexes
```sql
-- Index hanya untuk active groups
CREATE INDEX idx_active_groups ON groups(created_at) 
WHERE is_private = false;

-- Index untuk recent messages
CREATE INDEX idx_recent_messages ON messages(created_at) 
WHERE created_at > NOW() - INTERVAL '30 days';
```

#### 3. Full-Text Search
```sql
-- Search messages
CREATE INDEX idx_messages_search ON messages 
USING gin(to_tsvector('english', content));

-- Search notes
CREATE INDEX idx_notes_search ON notes 
USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));
```

### Query Optimization

#### 1. Pagination dengan Cursor
```typescript
// Efficient pagination untuk messages
const getMessages = async (groupId: string, cursor?: string, limit = 50) => {
  return await prisma.message.findMany({
    where: {
      groupId,
      ...(cursor && { id: { lt: cursor } })
    },
    orderBy: { createdAt: 'desc' },
    take: limit,
    include: {
      author: {
        select: { id: true, username: true, name: true, avatar: true }
      }
    }
  })
}
```

#### 2. Batch Operations
```typescript
// Bulk insert note blocks
const createNoteBlocks = async (noteId: string, blocks: BlockInput[]) => {
  return await prisma.noteBlock.createMany({
    data: blocks.map((block, index) => ({
      ...block,
      noteId,
      order: index
    }))
  })
}
```

#### 3. Connection Pooling
```typescript
// Prisma connection pooling
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL + "?connection_limit=20&pool_timeout=20"
    }
  }
})
```

## 🔒 Security Considerations

### 1. Data Validation
```typescript
// Input validation dengan Zod
const createMessageSchema = z.object({
  content: z.string().min(1).max(2000),
  groupId: z.string().cuid()
})

// Database constraints
ALTER TABLE messages ADD CONSTRAINT check_content_length 
CHECK (char_length(content) BETWEEN 1 AND 2000);
```

### 2. Access Control
```typescript
// Row-level security simulation
const getUserMessages = async (userId: string, groupId: string) => {
  // Verify membership first
  const membership = await prisma.groupMember.findFirst({
    where: { userId, groupId }
  })
  
  if (!membership) {
    throw new Error('Access denied')
  }
  
  return await prisma.message.findMany({
    where: { groupId }
  })
}
```

### 3. SQL Injection Prevention
```typescript
// Prisma automatically prevents SQL injection
const searchMessages = async (query: string) => {
  // Safe - Prisma handles parameterization
  return await prisma.message.findMany({
    where: {
      content: {
        contains: query,
        mode: 'insensitive'
      }
    }
  })
}
```

## 📈 Monitoring dan Maintenance

### 1. Query Performance
```sql
-- Monitor slow queries
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC;

-- Index usage
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE tablename IN ('users', 'groups', 'messages');
```

### 2. Database Health
```sql
-- Table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Connection monitoring
SELECT state, count(*) 
FROM pg_stat_activity 
GROUP BY state;
```

### 3. Backup Strategy
```bash
# Daily backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Point-in-time recovery setup
# Enable WAL archiving in postgresql.conf
archive_mode = on
archive_command = 'cp %p /backup/archive/%f'
```

## 🚀 Scaling Considerations

### Vertical Scaling
- **CPU**: Increase untuk complex queries
- **Memory**: Increase untuk larger working sets
- **Storage**: SSD untuk better I/O performance

### Horizontal Scaling Options
1. **Read Replicas**: Untuk read-heavy workloads
2. **Sharding**: Partition data by group_id
3. **Connection Pooling**: PgBouncer untuk connection management

### Caching Strategy
```typescript
// Redis caching untuk frequent queries
const getCachedUser = async (userId: string) => {
  const cached = await redis.get(`user:${userId}`)
  if (cached) return JSON.parse(cached)
  
  const user = await prisma.user.findUnique({
    where: { id: userId }
  })
  
  await redis.setex(`user:${userId}`, 300, JSON.stringify(user))
  return user
}
```

## 🔄 Migration dan Deployment

### Development Workflow
```bash
# 1. Create migration
npx prisma migrate dev --name add_feature

# 2. Generate client
npx prisma generate

# 3. Seed database
npx prisma db seed
```

### Production Deployment
```bash
# 1. Backup database
pg_dump $DATABASE_URL > pre_migration_backup.sql

# 2. Deploy migration
npx prisma migrate deploy

# 3. Verify deployment
npx prisma db pull
```

### Rollback Strategy
```sql
-- Create rollback script for each migration
-- migration_rollback_001.sql
DROP INDEX IF EXISTS idx_new_feature;
ALTER TABLE users DROP COLUMN IF EXISTS new_field;
```

Database PostgreSQL dengan Prisma ORM memberikan foundation yang solid untuk MyBinder dengan type safety, performance yang baik, dan maintainability yang tinggi. Struktur relational yang well-designed mendukung semua fitur aplikasi dengan room untuk growth di masa depan.
