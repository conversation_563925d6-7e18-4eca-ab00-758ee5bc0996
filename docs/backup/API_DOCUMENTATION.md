# Dokumentasi API MyBinder

## Ringkasan

MyBinder menggunakan Next.js API Routes untuk menyediakan RESTful API yang mendukung semua fitur aplikasi. API dirancang dengan prinsip REST, type-safe dengan TypeScript, dan dilengkapi dengan authentication, validation, dan error handling yang robust.

## 🏗️ Arsitektur API

### Jenis API: RESTful API dengan Next.js API Routes

#### Mengapa REST API?

1. **Simplicity**: Mudah dipahami dan diimplementasikan
2. **Stateless**: Setiap request independent, mudah di-scale
3. **HTTP Methods**: Memanfaatkan HTTP verbs (GET, POST, PUT, DELETE)
4. **Caching**: HTTP caching mechanisms
5. **Tooling**: Excellent tooling dan debugging support

#### Perbandingan dengan Alternatif

| API Type | Kelebihan | Kekurangan | Skor | Alasan |
|----------|-----------|------------|------|--------|
| **REST** ✅ | Simple, cacheable, stateless | Over/under-fetching | 9/10 | **Dipilih**: Perfect untuk CRUD operations |
| **GraphQL** | Flexible queries, type-safe | Complex setup, learning curve | 7/10 | Overkill untuk aplikasi ini |
| **tRPC** | Type-safe, great DX | TypeScript only, newer | 8/10 | Good alternative untuk TypeScript apps |
| **gRPC** | Performance, streaming | Complex, binary protocol | 6/10 | Tidak cocok untuk web apps |

### Next.js API Routes vs Alternatif

| Framework | Kelebihan | Kekurangan | Skor | Alasan |
|-----------|-----------|------------|------|--------|
| **Next.js API Routes** ✅ | Integrated, file-based routing | Vendor lock-in | 9/10 | **Dipilih**: Perfect integration dengan frontend |
| **Express.js** | Mature, flexible, middleware | Separate server, more setup | 7/10 | Additional complexity |
| **Fastify** | Fast, schema-based | Less ecosystem | 7/10 | Tidak perlu untuk use case ini |
| **NestJS** | Enterprise, decorators | Heavy, learning curve | 6/10 | Overkill untuk aplikasi medium |

## 📁 Struktur API

### File-based Routing Structure

```
src/app/api/
├── auth/
│   ├── login/route.ts          # POST /api/auth/login
│   ├── register/route.ts       # POST /api/auth/register
│   └── logout/route.ts         # POST /api/auth/logout
├── groups/
│   ├── route.ts                # GET, POST /api/groups
│   └── [groupId]/
│       ├── route.ts            # GET, PUT, DELETE /api/groups/:id
│       ├── members/route.ts    # GET, POST /api/groups/:id/members
│       └── messages/
│           └── route.ts        # GET, POST /api/groups/:id/messages
├── notes/
│   ├── route.ts                # GET, POST /api/notes
│   └── [noteId]/
│       ├── route.ts            # GET, PUT, DELETE /api/notes/:id
│       └── blocks/route.ts     # GET, POST /api/notes/:id/blocks
└── socket/
    └── route.ts                # Socket.io endpoint
```

### API Conventions

#### 1. HTTP Methods
- **GET**: Retrieve data (idempotent)
- **POST**: Create new resources
- **PUT**: Update entire resource
- **PATCH**: Partial update (not used currently)
- **DELETE**: Remove resource

#### 2. Status Codes
```typescript
// Success responses
200 OK          // Successful GET, PUT
201 Created     // Successful POST
204 No Content  // Successful DELETE

// Client errors
400 Bad Request     // Validation errors
401 Unauthorized    // Authentication required
403 Forbidden       // Access denied
404 Not Found       // Resource not found
409 Conflict        // Duplicate resource

// Server errors
500 Internal Server Error  // Unexpected errors
```

#### 3. Response Format
```typescript
// Success response
{
  "message": "Operation successful",
  "data": { /* response data */ },
  "pagination": { /* pagination info */ }  // for list endpoints
}

// Error response
{
  "error": "Error message",
  "details": [ /* validation errors */ ]   // optional
}
```

## 🔐 Authentication & Authorization

### JWT-based Authentication

#### Authentication Flow
```typescript
// 1. Login endpoint
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

// Response
{
  "message": "Login successful",
  "data": {
    "user": { "id": "...", "email": "...", "username": "..." },
    "token": "eyJhbGciOiJIUzI1NiIs..."
  }
}

// 2. Protected endpoint usage
GET /api/groups
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
```

#### Middleware Implementation
```typescript
// src/lib/middleware.ts
export async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  const token = authHeader?.replace('Bearer ', '')
  
  if (!token) return null
  
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, email: true, username: true, name: true }
    })
    return user
  } catch {
    return null
  }
}

// Usage in API routes
export async function GET(request: NextRequest) {
  const user = await getAuthenticatedUser(request)
  if (!user) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
  // ... rest of the handler
}
```

### Authorization Patterns

#### 1. Resource Ownership
```typescript
// Check if user owns the resource
const group = await prisma.group.findUnique({
  where: { id: groupId }
})

if (group.ownerId !== user.id) {
  return NextResponse.json({ error: 'Access denied' }, { status: 403 })
}
```

#### 2. Group Membership
```typescript
// Check if user is member of group
const membership = await prisma.groupMember.findFirst({
  where: { userId: user.id, groupId: groupId }
})

if (!membership) {
  return NextResponse.json({ error: 'Not a member of this group' }, { status: 403 })
}
```

#### 3. Role-based Access
```typescript
// Check if user has required role
const isOwnerOrAdmin = membership.role === 'OWNER' || membership.role === 'ADMIN'

if (!isOwnerOrAdmin) {
  return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
}
```

## 📋 API Endpoints Documentation

### Authentication Endpoints

#### POST /api/auth/register
```typescript
// Request
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "name": "User Name"  // optional
}

// Response (201)
{
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "clx1234567890",
      "email": "<EMAIL>",
      "username": "username",
      "name": "User Name"
    },
    "token": "eyJhbGciOiJIUzI1NiIs..."
  }
}
```

#### POST /api/auth/login
```typescript
// Request
{
  "email": "<EMAIL>",  // or username
  "password": "password123"
}

// Response (200)
{
  "message": "Login successful",
  "data": {
    "user": { /* user data */ },
    "token": "eyJhbGciOiJIUzI1NiIs..."
  }
}
```

### Groups Endpoints

#### GET /api/groups
```typescript
// Headers: Authorization: Bearer <token>

// Response (200)
{
  "message": "Groups retrieved successfully",
  "data": [
    {
      "id": "clx1234567890",
      "name": "My Group",
      "description": "Group description",
      "isPrivate": false,
      "ownerId": "clx0987654321",
      "createdAt": "2024-01-01T00:00:00Z",
      "owner": { "id": "...", "username": "...", "name": "..." },
      "members": [
        {
          "id": "...",
          "role": "OWNER",
          "user": { "id": "...", "username": "...", "name": "..." }
        }
      ],
      "_count": {
        "members": 5,
        "messages": 42,
        "notes": 3
      }
    }
  ]
}
```

#### POST /api/groups
```typescript
// Request
{
  "name": "New Group",
  "description": "Group description",  // optional
  "isPrivate": false                   // optional, default false
}

// Response (201)
{
  "message": "Group created successfully",
  "group": { /* group data with owner and members */ }
}
```

#### GET /api/groups/[groupId]
```typescript
// Response (200)
{
  "message": "Group retrieved successfully",
  "data": {
    "group": { /* detailed group data */ }
  }
}
```

### Messages Endpoints

#### GET /api/groups/[groupId]/messages
```typescript
// Query params: ?page=1&limit=50

// Response (200)
{
  "message": "Messages retrieved successfully",
  "messages": [
    {
      "id": "clx1234567890",
      "content": "Hello world!",
      "createdAt": "2024-01-01T00:00:00Z",
      "author": {
        "id": "clx0987654321",
        "username": "user123",
        "name": "User Name",
        "avatar": null
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 50,
    "total": 150,
    "hasMore": true
  }
}
```

#### POST /api/groups/[groupId]/messages
```typescript
// Request
{
  "content": "Hello world!"
}

// Response (201)
{
  "message": "Message sent successfully",
  "data": {
    "id": "clx1234567890",
    "content": "Hello world!",
    "createdAt": "2024-01-01T00:00:00Z",
    "author": { /* author data */ }
  }
}
```

### Notes Endpoints

#### GET /api/notes
```typescript
// Query params: ?groupId=clx123

// Response (200)
{
  "message": "Notes retrieved successfully",
  "data": [
    {
      "id": "clx1234567890",
      "title": "Meeting Notes",
      "description": "Notes from team meeting",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T01:00:00Z",
      "author": { /* author data */ },
      "_count": { "blocks": 5 }
    }
  ]
}
```

#### POST /api/notes
```typescript
// Request
{
  "title": "New Note",
  "description": "Note description",  // optional
  "groupId": "clx1234567890"
}

// Response (201)
{
  "message": "Note created successfully",
  "data": { /* note data */ }
}
```

## ✅ Validation & Error Handling

### Input Validation dengan Zod

```typescript
import { z } from 'zod'

// Schema definitions
const createGroupSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  isPrivate: z.boolean().optional()
})

const createMessageSchema = z.object({
  content: z.string().min(1, 'Message content is required').max(2000, 'Message too long')
})

// Usage in API route
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, isPrivate } = createGroupSchema.parse(body)
    
    // Process valid data...
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      )
    }
    // Handle other errors...
  }
}
```

### Error Handling Patterns

#### 1. Centralized Error Handler
```typescript
// src/lib/errorHandler.ts
export function handleApiError(error: unknown) {
  console.error('API Error:', error)
  
  if (error instanceof z.ZodError) {
    return NextResponse.json(
      { error: 'Validation error', details: error.issues },
      { status: 400 }
    )
  }
  
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    if (error.code === 'P2002') {
      return NextResponse.json(
        { error: 'Resource already exists' },
        { status: 409 }
      )
    }
  }
  
  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}
```

#### 2. Custom Error Classes
```typescript
export class ApiError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, details?: any) {
    super(message, 400, details)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends ApiError {
  constructor(message: string = 'Authentication required') {
    super(message, 401)
    this.name = 'AuthenticationError'
  }
}
```

## 🚀 Performance & Optimization

### 1. Database Query Optimization
```typescript
// Efficient pagination
const getMessages = async (groupId: string, page: number, limit: number) => {
  const offset = (page - 1) * limit
  
  const [messages, total] = await Promise.all([
    prisma.message.findMany({
      where: { groupId },
      orderBy: { createdAt: 'desc' },
      skip: offset,
      take: limit,
      include: {
        author: {
          select: { id: true, username: true, name: true, avatar: true }
        }
      }
    }),
    prisma.message.count({ where: { groupId } })
  ])
  
  return {
    messages,
    pagination: {
      page,
      limit,
      total,
      hasMore: offset + limit < total
    }
  }
}
```

### 2. Response Caching
```typescript
// Cache headers untuk static data
export async function GET(request: NextRequest) {
  const response = NextResponse.json(data)
  
  // Cache for 5 minutes
  response.headers.set('Cache-Control', 'public, max-age=300')
  
  return response
}
```

### 3. Request Deduplication
```typescript
// Prevent duplicate requests
const requestCache = new Map()

export async function POST(request: NextRequest) {
  const body = await request.json()
  const requestKey = JSON.stringify(body)
  
  if (requestCache.has(requestKey)) {
    return requestCache.get(requestKey)
  }
  
  const responsePromise = processRequest(body)
  requestCache.set(requestKey, responsePromise)
  
  // Clear cache after 5 seconds
  setTimeout(() => requestCache.delete(requestKey), 5000)
  
  return responsePromise
}
```

## 📊 API Monitoring & Analytics

### 1. Request Logging
```typescript
// Middleware untuk logging
export async function middleware(request: NextRequest) {
  const start = Date.now()
  
  console.log(`${request.method} ${request.url} - Started`)
  
  const response = await NextResponse.next()
  
  const duration = Date.now() - start
  console.log(`${request.method} ${request.url} - ${response.status} (${duration}ms)`)
  
  return response
}
```

### 2. Error Tracking
```typescript
// Error reporting
export function reportError(error: Error, context: any) {
  console.error('API Error:', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  })
  
  // Send to monitoring service (Sentry, etc.)
  // sentry.captureException(error, { extra: context })
}
```

### 3. Performance Metrics
```typescript
// Track API performance
const metrics = {
  requestCount: 0,
  averageResponseTime: 0,
  errorRate: 0
}

export function trackMetrics(duration: number, isError: boolean) {
  metrics.requestCount++
  metrics.averageResponseTime = 
    (metrics.averageResponseTime + duration) / 2
  
  if (isError) {
    metrics.errorRate = 
      (metrics.errorRate * (metrics.requestCount - 1) + 1) / metrics.requestCount
  }
}
```

## 🔄 API Versioning Strategy

### Current Approach: No Versioning
Saat ini API tidak menggunakan versioning karena masih dalam development aktif.

### Future Versioning Options

#### 1. URL Versioning
```
/api/v1/groups
/api/v2/groups
```

#### 2. Header Versioning
```
GET /api/groups
API-Version: v1
```

#### 3. Accept Header Versioning
```
GET /api/groups
Accept: application/vnd.mybinder.v1+json
```

## 🧪 Testing Strategy

### 1. Unit Tests untuk API Routes
```typescript
// __tests__/api/auth/login.test.ts
import { POST } from '@/app/api/auth/login/route'

describe('/api/auth/login', () => {
  it('should login with valid credentials', async () => {
    const request = new Request('http://localhost/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    })
    
    const response = await POST(request)
    const data = await response.json()
    
    expect(response.status).toBe(200)
    expect(data.data.token).toBeDefined()
  })
})
```

### 2. Integration Tests
```typescript
// Test complete API flows
describe('Group Management Flow', () => {
  it('should create group, add members, and send messages', async () => {
    // 1. Create group
    const groupResponse = await createGroup(authToken, groupData)
    
    // 2. Add member
    const memberResponse = await addMember(authToken, groupId, memberData)
    
    // 3. Send message
    const messageResponse = await sendMessage(authToken, groupId, messageData)
    
    expect(messageResponse.status).toBe(201)
  })
})
```

API MyBinder dirancang dengan prinsip REST yang clean, type-safe dengan TypeScript, dan dilengkapi dengan security, validation, dan error handling yang robust. Struktur yang modular memungkinkan easy maintenance dan scaling di masa depan.
